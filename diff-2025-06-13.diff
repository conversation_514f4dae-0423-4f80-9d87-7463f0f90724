diff --git a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/dao/FenceViolationOfAgreementAppealDao.java b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/dao/FenceViolationOfAgreementAppealDao.java
index a5d805b3aa..7f269b81a1 100644
--- a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/dao/FenceViolationOfAgreementAppealDao.java
+++ b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/dao/FenceViolationOfAgreementAppealDao.java
@@ -1,6 +1,7 @@
 package com.intelliquor.cloud.shop.common.dao;
 
 import com.baomidou.mybatisplus.core.mapper.BaseMapper;
+import com.intelliquor.cloud.shop.common.basequery.ExtBaseMapper;
 import com.intelliquor.cloud.shop.common.model.FenceViolationOfAgreementAppealModel;
 import com.intelliquor.cloud.shop.common.model.resp.FenceViolationOfAgreementAppealModelResp;
 import org.apache.ibatis.annotations.Param;
@@ -16,11 +17,19 @@ import java.util.Map;
  * <AUTHOR>
  * @since 2024-03-07
  */
-public interface FenceViolationOfAgreementAppealDao extends BaseMapper<FenceViolationOfAgreementAppealModel> {
+public interface FenceViolationOfAgreementAppealDao extends ExtBaseMapper<FenceViolationOfAgreementAppealModel> {
 
     List<FenceViolationOfAgreementAppealModelResp> appealList(FenceViolationOfAgreementAppealModel req);
 
     List<FenceViolationOfAgreementAppealModelResp> exportData(Map<String, Object> search);
 
     FenceViolationOfAgreementAppealModel selectByFenceId(@Param("FenceId") Integer fenceId);
+
+    /**
+     * 批量插入申诉记录
+     *
+     * @param appealList 申诉记录列表
+     * @return 插入的记录数
+     */
+    int batchInsert(@Param("appealList") List<FenceViolationOfAgreementAppealModel> appealList);
 }
diff --git a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/model/req/BatchAppealServiceReq.java b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/model/req/BatchAppealServiceReq.java
new file mode 100644
index 0000000000..8fca674220
--- /dev/null
+++ b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/model/req/BatchAppealServiceReq.java
@@ -0,0 +1,52 @@
+package com.intelliquor.cloud.shop.common.model.req;
+
+import lombok.Getter;
+import lombok.Setter;
+
+import java.util.List;
+
+/**
+ * 批量申诉Service层请求参数
+ *
+ * <AUTHOR>
+ * @since 2024-01-01
+ */
+@Getter
+@Setter
+public class BatchAppealServiceReq {
+
+    /**
+     * 违约记录ID列表
+     */
+    private List<Integer> violationIds;
+
+    /**
+     * 申诉说明
+     */
+    private String reason;
+
+    /**
+     * 申诉附件URL
+     */
+    private String appealFileUrl;
+
+    /**
+     * 创建人ID
+     */
+    private Integer createUser;
+
+    /**
+     * 创建人姓名
+     */
+    private String createUserName;
+
+    /**
+     * 公司ID
+     */
+    private Integer companyId;
+
+    /**
+     * 申诉人电话
+     */
+    private String appealUserTel;
+} 
\ No newline at end of file
diff --git a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/model/req/BatchFenceViolationAppealReq.java b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/model/req/BatchFenceViolationAppealReq.java
new file mode 100644
index 0000000000..144cea1167
--- /dev/null
+++ b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/model/req/BatchFenceViolationAppealReq.java
@@ -0,0 +1,38 @@
+package com.intelliquor.cloud.shop.common.model.req;
+
+import lombok.Getter;
+import lombok.Setter;
+
+import javax.validation.constraints.NotEmpty;
+import javax.validation.constraints.Size;
+import java.util.List;
+
+/**
+ * 批量违约申诉请求DTO
+ *
+ * <AUTHOR>
+ * @since 2024-01-01
+ */
+@Getter
+@Setter
+public class BatchFenceViolationAppealReq {
+
+    /**
+     * 违约记录ID列表
+     */
+    @NotEmpty(message = "违约记录ID列表不能为空")
+    @Size(min = 1, max = 100, message = "批量申诉数量必须在1-100之间")
+    private List<Integer> violationIds;
+
+    /**
+     * 申诉说明
+     */
+    @NotEmpty(message = "申诉说明不能为空")
+    private String reason;
+
+    /**
+     * 申诉附件URL（JSON格式）
+     */
+    @NotEmpty(message = "申诉附件不能为空")
+    private String appealFileUrl;
+} 
\ No newline at end of file
diff --git a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/model/req/FenceViolateOpenBottleAreaRecordReq.java b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/model/req/FenceViolateOpenBottleAreaRecordReq.java
index cb3e419947..4691b41213 100644
--- a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/model/req/FenceViolateOpenBottleAreaRecordReq.java
+++ b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/model/req/FenceViolateOpenBottleAreaRecordReq.java
@@ -27,6 +27,11 @@ public class FenceViolateOpenBottleAreaRecordReq {
      */
     private String qrCode;
 
+    /**
+     * 箱码
+     */
+    private String boxCode;
+
     /**
      * 开瓶开始时间
      */
diff --git a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/model/resp/BatchFenceViolationAppealResp.java b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/model/resp/BatchFenceViolationAppealResp.java
new file mode 100644
index 0000000000..1c5b75ede4
--- /dev/null
+++ b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/model/resp/BatchFenceViolationAppealResp.java
@@ -0,0 +1,49 @@
+package com.intelliquor.cloud.shop.common.model.resp;
+
+import lombok.Getter;
+import lombok.Setter;
+
+import java.util.List;
+
+/**
+ * 批量违约申诉响应DTO
+ *
+ * <AUTHOR>
+ * @since 2024-01-01
+ */
+@Getter
+@Setter
+public class BatchFenceViolationAppealResp {
+
+    /**
+     * 申诉成功的违约记录ID列表
+     */
+    private List<Integer> successIds;
+
+    /**
+     * 申诉失败的违约记录详情
+     */
+    private List<FailedAppealItem> failedItems;
+
+    /**
+     * 申诉失败详情
+     */
+    @Getter
+    @Setter
+    public static class FailedAppealItem {
+        /**
+         * 违约记录ID
+         */
+        private Integer violationId;
+
+        /**
+         * 失败原因
+         */
+        private String failureReason;
+
+        public FailedAppealItem(Integer violationId, String failureReason) {
+            this.violationId = violationId;
+            this.failureReason = failureReason;
+        }
+    }
+} 
\ No newline at end of file
diff --git a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/FenceViolateOpenBottleAreaRecordService.java b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/FenceViolateOpenBottleAreaRecordService.java
index fedde00ff2..05bff0b9aa 100644
--- a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/FenceViolateOpenBottleAreaRecordService.java
+++ b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/FenceViolateOpenBottleAreaRecordService.java
@@ -3,7 +3,9 @@ package com.intelliquor.cloud.shop.common.service;
 import com.baomidou.mybatisplus.extension.service.IService;
 import com.intelliquor.cloud.shop.common.model.FenceViolateOpenBottleAreaRecordModel;
 import com.intelliquor.cloud.shop.common.model.FenceViolationOfAgreementAppealModel;
+import com.intelliquor.cloud.shop.common.model.req.BatchAppealServiceReq;
 import com.intelliquor.cloud.shop.common.model.req.FenceViolateOpenBottleAreaRecordReq;
+import com.intelliquor.cloud.shop.common.model.resp.BatchFenceViolationAppealResp;
 import com.intelliquor.cloud.shop.common.model.resp.FenceViolateOpenBottleAreaRecordResp;
 
 import java.util.List;
@@ -33,6 +35,14 @@ public interface FenceViolateOpenBottleAreaRecordService extends IService<FenceV
      */
     void addAppeal(FenceViolationOfAgreementAppealModel fenceViolationOfAgreementAppealModel);
 
+    /**
+     * 批量发起申诉
+     *
+     * @param batchAppealReq 批量申诉参数
+     * @return 批量申诉结果
+     */
+    BatchFenceViolationAppealResp batchAddAppeal(BatchAppealServiceReq batchAppealReq);
+
     /**
      * 处理异地类型数据
      */
diff --git a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/impl/FenceViolateOpenBottleAreaRecordServiceImpl.java b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/impl/FenceViolateOpenBottleAreaRecordServiceImpl.java
index 6354e5b47d..68a3e9c304 100644
--- a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/impl/FenceViolateOpenBottleAreaRecordServiceImpl.java
+++ b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/impl/FenceViolateOpenBottleAreaRecordServiceImpl.java
@@ -13,17 +13,19 @@ import com.intelliquor.cloud.shop.common.dao.*;
 import com.intelliquor.cloud.shop.common.enums.*;
 import com.intelliquor.cloud.shop.common.exception.BusinessException;
 import com.intelliquor.cloud.shop.common.model.*;
+import com.intelliquor.cloud.shop.common.model.req.BatchAppealServiceReq;
 import com.intelliquor.cloud.shop.common.model.req.FenceViolateOpenBottleAreaRecordReq;
 import com.intelliquor.cloud.shop.common.model.resp.ActivityExceptionComplaintConfigResp;
+import com.intelliquor.cloud.shop.common.model.resp.BatchFenceViolationAppealResp;
 import com.intelliquor.cloud.shop.common.model.resp.FenceViolateOpenBottleAreaRecordResp;
 import com.intelliquor.cloud.shop.common.model.resp.RewardResp;
 import com.intelliquor.cloud.shop.common.service.ActivityRewardExceptionRecordService;
 import com.intelliquor.cloud.shop.common.service.FenceViolateOpenBottleAreaRecordService;
 import com.intelliquor.cloud.shop.common.service.IDealerContractRelCommonService;
-import com.intelliquor.cloud.shop.common.service.TerminalBrokerTaskRatioConfigCommonService;
 import lombok.RequiredArgsConstructor;
 import lombok.extern.slf4j.Slf4j;
 import org.apache.commons.lang3.StringUtils;
+import org.springframework.aop.framework.AopContext;
 import org.springframework.data.redis.core.RedisTemplate;
 import org.springframework.stereotype.Service;
 import org.springframework.transaction.annotation.Transactional;
@@ -572,6 +574,196 @@ public class FenceViolateOpenBottleAreaRecordServiceImpl extends ServiceImpl<Fen
         fenceViolationOfAgreementAppealDao.insert(model);
     }
 
+    @Override
+    public BatchFenceViolationAppealResp batchAddAppeal(BatchAppealServiceReq batchAppealReq) {
+        log.info("批量申诉开始, 违约记录ID列表：{}, 申诉人：{}", batchAppealReq.getViolationIds(), batchAppealReq.getCreateUserName());
+        
+        BatchFenceViolationAppealResp response = new BatchFenceViolationAppealResp();
+        response.setSuccessIds(new ArrayList<>());
+        response.setFailedItems(new ArrayList<>());
+        
+        // 基础参数校验
+        if (CollectionUtils.isEmpty(batchAppealReq.getViolationIds())) {
+            throw new BusinessException("违约记录ID列表不能为空");
+        }
+        if (batchAppealReq.getViolationIds().size() > 100) {
+            throw new BusinessException("批量申诉数量不能超过100条");
+        }
+        if (StringUtils.isBlank(batchAppealReq.getReason())) {
+            throw new BusinessException("申诉说明不能为空");
+        }
+        if (StringUtils.isBlank(batchAppealReq.getAppealFileUrl())) {
+            throw new BusinessException("申诉附件不能为空");
+        }
+        
+        Date currentTime = new Date();
+        
+        // 1. 批量查询违约记录 - 使用LambdaQuery
+        List<FenceViolateOpenBottleAreaRecordModel> fenceRecords = lambdaQuery()
+                .in(FenceViolateOpenBottleAreaRecordModel::getId, batchAppealReq.getViolationIds())
+                .list();
+        
+        // 构建ID到记录的映射
+        Map<Integer, FenceViolateOpenBottleAreaRecordModel> fenceRecordMap = fenceRecords.stream()
+                .collect(Collectors.toMap(record -> record.getId().intValue(), record -> record));
+        
+        // 2. 批量查询现有申诉记录 - 使用LambdaQuery  
+        LambdaQueryWrapper<FenceViolationOfAgreementAppealModel> appealQuery = Wrappers.lambdaQuery();
+        appealQuery.in(FenceViolationOfAgreementAppealModel::getViolationOfAgreementInfoId, batchAppealReq.getViolationIds())
+                .eq(FenceViolationOfAgreementAppealModel::getIsDelete, IsDelete.DELETE_FALSE.getCode());
+        List<FenceViolationOfAgreementAppealModel> existingAppeals = fenceViolationOfAgreementAppealDao.selectList(appealQuery);
+        Set<Integer> existingAppealViolationIds = existingAppeals.stream()
+                .map(FenceViolationOfAgreementAppealModel::getViolationOfAgreementInfoId)
+                .collect(Collectors.toSet());
+        
+        // 3. 收集所有需要查询的经销商编码（去重）
+        Set<String> dealerCodes = fenceRecords.stream()
+                .filter(record -> StringUtils.isNotBlank(record.getViolateDealerCode()))
+                .map(FenceViolateOpenBottleAreaRecordModel::getViolateDealerCode)
+                .collect(Collectors.toSet());
+        
+        // 4. 批量查询经销商申诉权限配置 - 使用LambdaQuery
+        Map<String, ActivityExceptionComplaintConfigResp> dealerConfigMap = new HashMap<>();
+        if (!dealerCodes.isEmpty()) {
+            LambdaQueryWrapper<ActivityExceptionComplaintConfigModel> configQuery = Wrappers.lambdaQuery();
+            configQuery.in(ActivityExceptionComplaintConfigModel::getDealerCode, dealerCodes)
+                    .eq(ActivityExceptionComplaintConfigModel::getDeleteStatus, 0);
+            List<ActivityExceptionComplaintConfigModel> configs = activityExceptionComplaintConfigDao.selectList(configQuery);
+            
+            // 转换为响应对象
+            dealerConfigMap = configs.stream()
+                    .collect(Collectors.toMap(
+                        ActivityExceptionComplaintConfigModel::getDealerCode, 
+                        config -> {
+                            ActivityExceptionComplaintConfigResp resp = new ActivityExceptionComplaintConfigResp();
+                            resp.setDealerCode(config.getDealerCode());
+                            resp.setUseStatus(config.getUseStatus());
+                            return resp;
+                        }
+                    ));
+        }
+        
+        // 5. 数据验证和分批处理 - 逐个处理每条记录，确保单条错误不影响其他记录
+        List<Integer> validViolationIds = new ArrayList<>();
+        List<FenceViolationOfAgreementAppealModel> appealsToInsert = new ArrayList<>();
+        
+        for (Integer violationId : batchAppealReq.getViolationIds()) {
+            try {
+                // 检查违约记录是否存在
+                FenceViolateOpenBottleAreaRecordModel fenceRecord = fenceRecordMap.get(violationId);
+                if (Objects.isNull(fenceRecord)) {
+                    response.getFailedItems().add(new BatchFenceViolationAppealResp.FailedAppealItem(violationId, "未查询到违约记录"));
+                    continue;
+                }
+                
+                // 检查申诉状态
+                if (fenceRecord.getAppealStatus() != 0) {
+                    response.getFailedItems().add(new BatchFenceViolationAppealResp.FailedAppealItem(violationId, "违约记录已处理"));
+                    continue;
+                }
+                
+                // 检查是否已有申诉记录
+                if (existingAppealViolationIds.contains(violationId)) {
+                    response.getFailedItems().add(new BatchFenceViolationAppealResp.FailedAppealItem(violationId, "该记录已有申诉"));
+                    continue;
+                }
+                
+                // 检查经销商申诉权限
+                ActivityExceptionComplaintConfigResp config = dealerConfigMap.get(fenceRecord.getViolateDealerCode());
+                if (Objects.isNull(config)) {
+                    response.getFailedItems().add(new BatchFenceViolationAppealResp.FailedAppealItem(violationId, "经销商未配置申诉权限"));
+                    continue;
+                }
+                if (config.getUseStatus() != 0) {
+                    response.getFailedItems().add(new BatchFenceViolationAppealResp.FailedAppealItem(violationId, "经销商申诉权限已停用"));
+                    continue;
+                }
+                
+                // 验证通过，加入待处理列表
+                validViolationIds.add(violationId);
+                
+                // 准备插入的申诉记录
+                FenceViolationOfAgreementAppealModel appealToInsert = new FenceViolationOfAgreementAppealModel();
+                appealToInsert.setViolationOfAgreementInfoId(violationId);
+                appealToInsert.setReason(batchAppealReq.getReason());
+                appealToInsert.setAppealFileUrl(batchAppealReq.getAppealFileUrl());
+                appealToInsert.setCreateTime(currentTime);
+                appealToInsert.setCreateUser(batchAppealReq.getCreateUser());
+                appealToInsert.setCreateUserName(batchAppealReq.getCreateUserName());
+                appealToInsert.setCompanyId(batchAppealReq.getCompanyId());
+                appealToInsert.setAppealUserTel(batchAppealReq.getAppealUserTel());
+                appealToInsert.setIsDelete(IsDelete.DELETE_FALSE.getCode());
+                appealToInsert.setStatus(0); // 申诉中
+                appealToInsert.setAppealUser(batchAppealReq.getCreateUser());
+                appealToInsert.setAppealUserName(batchAppealReq.getCreateUserName());
+                appealsToInsert.add(appealToInsert);
+                
+                log.debug("批量申诉验证通过，违约记录ID：{}", violationId);
+                
+            } catch (Exception e) {
+                log.error("批量申诉处理违约记录ID：{} 时发生异常：{}", violationId, e.getMessage(), e);
+                response.getFailedItems().add(new BatchFenceViolationAppealResp.FailedAppealItem(violationId, "处理异常：" + e.getMessage()));
+            }
+        }
+        
+        // 6. 分批执行数据库操作 - 使用独立事务，确保部分失败不影响其他记录
+        if (!validViolationIds.isEmpty()) {
+            try {
+                // 通过AOP代理调用事务方法，确保事务注解生效
+                FenceViolateOpenBottleAreaRecordServiceImpl proxy = 
+                    (FenceViolateOpenBottleAreaRecordServiceImpl) AopContext.currentProxy();
+                proxy.processBatchAppealOperations(validViolationIds, appealsToInsert, response);
+            } catch (Exception e) {
+                log.error("批量申诉数据库操作失败：{}", e.getMessage(), e);
+                // 操作失败，将所有ID加入失败列表
+                validViolationIds.forEach(violationId -> 
+                    response.getFailedItems().add(new BatchFenceViolationAppealResp.FailedAppealItem(
+                        violationId, "数据库操作失败：" + e.getMessage()))
+                );
+            }
+        }
+        
+        log.info("批量申诉完成, 成功数量：{}, 失败数量：{}", response.getSuccessIds().size(), response.getFailedItems().size());
+        return response;
+    }
+
+    /**
+     * 分批处理申诉操作，使用独立事务确保单条记录错误不影响其他记录
+     * 注意：此方法需要通过AOP代理调用才能使事务注解生效
+     */
+    @Transactional(rollbackFor = Exception.class)
+    public void processBatchAppealOperations(List<Integer> validViolationIds, 
+                                           List<FenceViolationOfAgreementAppealModel> appealsToInsert,
+                                           BatchFenceViolationAppealResp response) {
+        
+        // 批量更新违约记录申诉状态
+        if (!validViolationIds.isEmpty()) {
+            boolean updateSuccess = lambdaUpdate()
+                    .set(FenceViolateOpenBottleAreaRecordModel::getAppealStatus, 1)
+                    .set(FenceViolateOpenBottleAreaRecordModel::getUpdateTime, new Date())
+                    .in(FenceViolateOpenBottleAreaRecordModel::getId, validViolationIds)
+                    .update();
+            
+            if (!updateSuccess) {
+                throw new BusinessException("批量更新违约记录申诉状态失败");
+            }
+            log.info("批量更新违约记录申诉状态完成，数量：{}", validViolationIds.size());
+        }
+        
+        // 批量插入申诉记录 - 使用XML实现
+        if (!appealsToInsert.isEmpty()) {
+            int insertCount = fenceViolationOfAgreementAppealDao.batchInsert(appealsToInsert);
+            if (insertCount != appealsToInsert.size()) {
+                throw new BusinessException("批量插入申诉记录失败，期望插入" + appealsToInsert.size() + "条，实际插入" + insertCount + "条");
+            }
+            log.info("批量插入申诉记录完成，数量：{}", insertCount);
+        }
+        
+        // 操作成功，将所有ID加入成功列表
+        response.getSuccessIds().addAll(validViolationIds);
+        log.info("批量申诉事务操作完成，成功处理数量：{}", validViolationIds.size());
+    }
+
     @Override
     public void dealFenceTypeData() {
         //1. 查询出 类型为异地 且异地类型为空的数据
diff --git a/shop-common/src/main/resources/mapper/FenceViolateOpenBottleAreaRecordDao.xml b/shop-common/src/main/resources/mapper/FenceViolateOpenBottleAreaRecordDao.xml
index a146e28590..5406311804 100644
--- a/shop-common/src/main/resources/mapper/FenceViolateOpenBottleAreaRecordDao.xml
+++ b/shop-common/src/main/resources/mapper/FenceViolateOpenBottleAreaRecordDao.xml
@@ -39,6 +39,9 @@
         <if test="qrCode != null and qrCode != ''">
             AND a.qr_code = #{qrCode}
         </if>
+        <if test="boxCode != null and boxCode != ''">
+            AND a.box_code = #{boxCode}
+        </if>
         <if test="fenceType != null">
             AND a.fence_type = #{fenceType}
         </if>
diff --git a/shop-terminal/src/main/java/com/intelliquor/cloud/shop/terminal/controller/applet/ViolateOpenBottleAreaRecordAppletController.java b/shop-terminal/src/main/java/com/intelliquor/cloud/shop/terminal/controller/applet/ViolateOpenBottleAreaRecordAppletController.java
index feff48c71f..cabb5a4b66 100644
--- a/shop-terminal/src/main/java/com/intelliquor/cloud/shop/terminal/controller/applet/ViolateOpenBottleAreaRecordAppletController.java
+++ b/shop-terminal/src/main/java/com/intelliquor/cloud/shop/terminal/controller/applet/ViolateOpenBottleAreaRecordAppletController.java
@@ -6,7 +6,10 @@ import com.intelliquor.cloud.shop.common.exception.BusinessException;
 import com.intelliquor.cloud.shop.common.exception.RestResponse;
 import com.intelliquor.cloud.shop.common.model.FenceViolationOfAgreementAppealModel;
 import com.intelliquor.cloud.shop.common.model.UserContext;
+import com.intelliquor.cloud.shop.common.model.req.BatchAppealServiceReq;
+import com.intelliquor.cloud.shop.common.model.req.BatchFenceViolationAppealReq;
 import com.intelliquor.cloud.shop.common.model.req.FenceViolateOpenBottleAreaRecordReq;
+import com.intelliquor.cloud.shop.common.model.resp.BatchFenceViolationAppealResp;
 import com.intelliquor.cloud.shop.common.model.resp.FenceViolateOpenBottleAreaRecordResp;
 import com.intelliquor.cloud.shop.common.service.FenceViolateOpenBottleAreaRecordService;
 import com.intelliquor.cloud.shop.terminal.service.IFenceViolateOpenBottleAreaRecordService;
@@ -53,6 +56,8 @@ public class ViolateOpenBottleAreaRecordAppletController {
         try {
             // 小程序端获取违约记录 过滤掉无需处理的白名单数据
             fenceViolateOpenBottleAreaRecordReq.setShowDealStatus("1");
+            // 小程序端不使用经销商名称搜索，由前端控制改为箱码搜索（SZYXPT-2138）
+            fenceViolateOpenBottleAreaRecordReq.setViolateDealerName(null);
             List<FenceViolateOpenBottleAreaRecordResp> list = iFenceViolateOpenBottleAreaRecordService.getTerminalPageList(page, limit, id, type, dealerIdOrShopId, fenceViolateOpenBottleAreaRecordReq);
             PageInfo<FenceViolateOpenBottleAreaRecordResp> pageInfo = new PageInfo<>(list);
             return RestResponse.success("查询成功", pageInfo);
@@ -93,4 +98,58 @@ public class ViolateOpenBottleAreaRecordAppletController {
         fenceViolateOpenBottleAreaRecordService.addAppeal(fenceViolationOfAgreementAppealModel);
         return RestResponse.success("申诉成功");
     }
+
+    /**
+     * 批量发起申诉
+     *
+     * @param request 批量申诉请求
+     * @return Response
+     */
+    @PostMapping("batchAddAppeal")
+    public RestResponse<BatchFenceViolationAppealResp> batchAddAppeal(@RequestBody @javax.validation.Valid BatchFenceViolationAppealReq request) {
+        try {
+            // 构建Service层请求参数
+            BatchAppealServiceReq serviceReq = buildBatchAppealServiceReq(request);
+            
+            // 调用批量申诉服务
+            BatchFenceViolationAppealResp response = fenceViolateOpenBottleAreaRecordService.batchAddAppeal(serviceReq);
+            
+            // 根据结果返回相应消息
+            return buildBatchAppealResponse(response);
+        } catch (Exception e) {
+            return RestResponse.error("批量申诉失败：" + e.getMessage());
+        }
+    }
+
+    /**
+     * 构建批量申诉Service层请求参数
+     */
+    private BatchAppealServiceReq buildBatchAppealServiceReq(BatchFenceViolationAppealReq request) {
+        // 获取当前用户信息（只调用一次）
+        com.intelliquor.cloud.shop.common.model.TerminalAccountManagerModel terminalModel = userContext.getTerminalModel();
+        
+        BatchAppealServiceReq serviceReq = new BatchAppealServiceReq();
+        serviceReq.setViolationIds(request.getViolationIds());
+        serviceReq.setReason(request.getReason());
+        serviceReq.setAppealFileUrl(request.getAppealFileUrl());
+        serviceReq.setCreateUser(terminalModel.getId());
+        serviceReq.setCreateUserName(terminalModel.getName());
+        serviceReq.setCompanyId(terminalModel.getCompanyId());
+        serviceReq.setAppealUserTel(terminalModel.getPhone());
+        
+        return serviceReq;
+    }
+
+    /**
+     * 构建批量申诉响应结果
+     */
+    private RestResponse<BatchFenceViolationAppealResp> buildBatchAppealResponse(BatchFenceViolationAppealResp response) {
+        if (response.getFailedItems().isEmpty()) {
+            return RestResponse.success("批量申诉全部成功", response);
+        } else if (response.getSuccessIds().isEmpty()) {
+            return RestResponse.success("批量申诉全部失败", response);
+        } else {
+            return RestResponse.success("批量申诉部分成功", response);
+        }
+    }
 }
