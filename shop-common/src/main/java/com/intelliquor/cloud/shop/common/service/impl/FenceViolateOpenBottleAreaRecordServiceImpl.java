package com.intelliquor.cloud.shop.common.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.intelliquor.cloud.shop.common.constant.RedisConstant;
import com.intelliquor.cloud.shop.common.dao.*;
import com.intelliquor.cloud.shop.common.enums.*;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.model.*;
import com.intelliquor.cloud.shop.common.model.req.BatchAppealServiceReq;
import com.intelliquor.cloud.shop.common.model.req.FenceViolateOpenBottleAreaRecordReq;
import com.intelliquor.cloud.shop.common.model.resp.ActivityExceptionComplaintConfigResp;
import com.intelliquor.cloud.shop.common.model.resp.BatchFenceViolationAppealResp;
import com.intelliquor.cloud.shop.common.model.resp.FenceViolateOpenBottleAreaRecordResp;
import com.intelliquor.cloud.shop.common.model.resp.RewardResp;
import com.intelliquor.cloud.shop.common.service.ActivityRewardExceptionRecordService;
import com.intelliquor.cloud.shop.common.service.FenceViolateOpenBottleAreaRecordService;
import com.intelliquor.cloud.shop.common.service.IDealerContractRelCommonService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【t_fence_violate_open_bottle_area_record(异地开瓶违约记录表)】的数据库操作Service实现
 * @createDate 2024-02-27 16:10:53
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FenceViolateOpenBottleAreaRecordServiceImpl extends ServiceImpl<FenceViolateOpenBottleAreaRecordDao, FenceViolateOpenBottleAreaRecordModel>
        implements FenceViolateOpenBottleAreaRecordService {

    private final FenceViolateOpenBottleAreaRecordDao fenceViolateOpenBottleAreaRecordDao;

    private final FenceDealerAuthAreaMapper fenceDealerAuthAreaMapper;

    private final ActivityRewardRecordDao activityRewardRecordDao;

    private final ActivityRewardExceptionRecordDao activityRewardExceptionRecordDao;

    @Resource
    private ActivityRewardExceptionRecordService activityRewardExceptionRecordService;

    @Resource
    private DealerInfoCommonDao dealerInfoCommonDao;

    @Resource
    private ConsumerScanDetailCommonDao consumerScanDetailCommonDao;

    @Resource
    private DealerContractRelCommonDao dealerContractRelCommonDao;

    @Resource
    private ShopDao shopDao;

    @Resource
    private FenceViolationOfAgreementSettingDao fenceViolationOfAgreementSettingDao;

    @Resource
    private ActivityExceptionComplaintConfigDao activityExceptionComplaintConfigDao;

    @Resource
    private FenceViolationOfAgreementAppealDao fenceViolationOfAgreementAppealDao;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private FenceDealerAuthMapper fenceDealerAuthMapper;


    @Resource
    private IDealerContractRelCommonService dealerContractRelCommonService;

    @Override
    public List<FenceViolateOpenBottleAreaRecordResp> getPageList(int page, int limit, FenceViolateOpenBottleAreaRecordReq fenceViolateOpenBottleAreaRecordReq) {
        PageHelper.startPage(page, limit);
        return fenceViolateOpenBottleAreaRecordDao.selectPageList(fenceViolateOpenBottleAreaRecordReq);
    }

    @Override
    public FenceViolateOpenBottleAreaRecordResp queryById(Integer id) {
        //查询异地违约基本信息
        FenceViolateOpenBottleAreaRecordResp resp = this.findById(id);

        //查询收发货记录
        List<DeliveryModel> deliveryModels = this.queryDeliveryList(resp);
        resp.setDeliveryModels(deliveryModels);

        //已发动销奖励
        List<RewardResp> dxRewardList = this.dxRewardList(resp);
        resp.setDxRewardList(dxRewardList);

        //白名单 已发开瓶奖励
        List<RewardResp> openRewardList = this.openRewardList(resp);
        resp.setOpenRewardList(openRewardList);

        //已扣留奖励记录
        List<RewardResp> rewardRespList = this.reserveRewardList(resp);
        resp.setReserveRewardList(rewardRespList);

        //已扣除奖励
        List<RewardResp> deductRewardList = this.deductRewardList(resp);
        resp.setDeductRewardList(deductRewardList);

        //经销商处罚违约明细
        List<RewardResp> violateDealerRewardList = this.violateDealerRewardList(resp);
        resp.setViolateDealerRewardList(violateDealerRewardList);
        //被侵权经销商奖励明细
        List<RewardResp> infringedDealerRewardList = this.infringedDealerRewardList(resp);
        resp.setInfringedDealerRewardList(infringedDealerRewardList);
        return resp;
    }

    /**
     * 查询异地违约基本信息
     *
     * @param id
     * @return
     */
    private FenceViolateOpenBottleAreaRecordResp findById(Integer id) {
        FenceViolateOpenBottleAreaRecordReq req = new FenceViolateOpenBottleAreaRecordReq();
        req.setId(id);
        List<FenceViolateOpenBottleAreaRecordResp> fenceViolateOpenBottleAreaRecordRespList = fenceViolateOpenBottleAreaRecordDao.selectPageList(req);
        if (CollectionUtils.isEmpty(fenceViolateOpenBottleAreaRecordRespList)) {
            throw new RuntimeException("未查询到数据信息");
        }
        if (fenceViolateOpenBottleAreaRecordRespList.size() > 1) {
            throw new RuntimeException("数据异常");
        }
        FenceViolateOpenBottleAreaRecordResp resp = fenceViolateOpenBottleAreaRecordRespList.get(0);
        //查询经销商/被侵权经销商区域并转换为字符串类型
        List<FenceDealerAuthArea> violateAreaList = fenceDealerAuthAreaMapper.getAreaListByDealerId(resp.getViolateDealerId());
        List<FenceDealerAuthArea> infringedAreaList = fenceDealerAuthAreaMapper.getAreaListByDealerId(resp.getInfringedDealerId());
        resp.setViolateDealerAuthArea(violateAreaList.stream().map(violateArea -> (StringUtils.isNotBlank(violateArea.getProvince()) ? violateArea.getProvince() : "")
                + (StringUtils.isNotBlank(violateArea.getCity()) ? violateArea.getCity() : "")
                + (StringUtils.isNotBlank(violateArea.getDistrict()) ? violateArea.getDistrict() : "") + "/").collect(Collectors.joining()));

        resp.setInfringedDealerAuthArea(infringedAreaList.stream().map(infringedArea -> (StringUtils.isNotBlank(infringedArea.getProvince()) ? infringedArea.getProvince() : "")
                + (StringUtils.isNotBlank(infringedArea.getCity()) ? infringedArea.getCity() : "")
                + (StringUtils.isNotBlank(infringedArea.getDistrict()) ? infringedArea.getDistrict() : "") + "/").collect(Collectors.joining()));

        List<FenceDealerAuthArea> violateAreasByContractCode = getAuthAreaList(resp.getViolateDealerId(), resp.getViolateContractCode());
        resp.setAuthAreaByViolateContract(violateAreasByContractCode.stream().map(violateArea -> (StringUtils.isNotBlank(violateArea.getProvince()) ? violateArea.getProvince() : "")
                + (StringUtils.isNotBlank(violateArea.getCity()) ? violateArea.getCity() : "")
                + (StringUtils.isNotBlank(violateArea.getDistrict()) ? violateArea.getDistrict() : "") + "/").collect(Collectors.joining()));

        List<FenceDealerAuthArea> infringedAreasByContractCode = getAuthAreaList(resp.getInfringedDealerId(), resp.getInfringedContractCode());
        resp.setAuthAreaByInfringedContract(infringedAreasByContractCode.stream().map(infringedArea -> (StringUtils.isNotBlank(infringedArea.getProvince()) ? infringedArea.getProvince() : "")
                + (StringUtils.isNotBlank(infringedArea.getCity()) ? infringedArea.getCity() : "")
                + (StringUtils.isNotBlank(infringedArea.getDistrict()) ? infringedArea.getDistrict() : "") + "/").collect(Collectors.joining()));
        return resp;
    }

    private  List<FenceDealerAuthArea> getAuthAreaList(Integer dealerId, String contractCode) {
        if (Objects.nonNull(dealerId) && StringUtils.isNotBlank(contractCode)) {
            LambdaQueryWrapper<FenceDealerAuth> lqwAuth = Wrappers.lambdaQuery();
            lqwAuth.eq(FenceDealerAuth::getDealerId, dealerId.longValue());
            lqwAuth.eq(FenceDealerAuth::getContractCode, contractCode);
            lqwAuth.eq(FenceDealerAuth::getIsDelete, IsDelete.DELETE_FALSE.getCode());
            List<FenceDealerAuth> fenceDealerAuthList = fenceDealerAuthMapper.selectList(lqwAuth);
            List<Long> authIdList = fenceDealerAuthList.stream().map(FenceDealerAuth::getId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(authIdList)) {
                LambdaQueryWrapper<FenceDealerAuthArea> lqwAuthArea = Wrappers.lambdaQuery();
                lqwAuthArea.in(FenceDealerAuthArea::getAuthId, authIdList);
                lqwAuthArea.eq(FenceDealerAuthArea::getIsDelete, IsDelete.DELETE_FALSE.getCode());
                return fenceDealerAuthAreaMapper.selectList(lqwAuthArea);
            }
        }
        return new ArrayList<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createFenceAreaData() {
        //查询本机ip
        String ip = "";
        try {
            ip = InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            log.error("异地违约处理，获取本机ip失败", e);
        }

        //每次处理数量
        Integer num = 100;
        //查询未处理的异地开瓶违约数据
        List<Long> scanIdList = fenceViolateOpenBottleAreaRecordDao.selectDealScanIdList();
        if (CollectionUtils.isEmpty(scanIdList)) {
            return;
        }
        log.info("开始处理异地开瓶违约数据，共{}条, scanId分别是:{}", scanIdList.size(), scanIdList);
        //截取处理的数据
        List<Long> dealScanIdList = scanIdList.subList(0, Math.min(num, scanIdList.size()));
        List<ActivityRewardExceptionRecordModel> fenceViolateOpenBottleAreaRecordModels = fenceViolateOpenBottleAreaRecordDao.selectUnprocessedFenceAreaData(dealScanIdList);
        fenceViolateOpenBottleAreaRecordModels = fenceViolateOpenBottleAreaRecordModels.stream()
                .filter(item -> Objects.equals(item.getRewardSubType(), ActivityRewardSubTypeEnum.OPEN_BOTTLE_NORMAL_REWARD.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fenceViolateOpenBottleAreaRecordModels)) {
            return;
        }

        //按照码进行分组
        Map<Integer, List<ActivityRewardExceptionRecordModel>> scanMap = fenceViolateOpenBottleAreaRecordModels.stream().collect(Collectors.groupingBy(ActivityRewardExceptionRecordModel::getOriginId));
        //遍历分组,使用
        for (Integer scanId : scanMap.keySet()) {
            log.info("服务器地址：{}，开始处理originId：{} 的开瓶违约数据", ip, scanId);
            //使用redis加锁,如果存在则跳过(防止重复处理，避免脏读)
            String redisTerminalIdKey = String.format(RedisConstant.FENCE_BOTTLE_WHITE, scanId);
            Boolean aBoolean = redisTemplate.opsForValue().setIfAbsent(redisTerminalIdKey, scanId, 20, TimeUnit.MINUTES);
            if (Boolean.FALSE.equals(aBoolean)) {
                continue;
            }
            //查询是否有已处理记录
            List<ActivityRewardExceptionRecordModel> list = activityRewardExceptionRecordDao.findDealtFenceRecordByOriginId(scanId);
            if (list.stream().anyMatch(item -> item.getFenceStatus() == 1)) {
                //更新违约记录状态
                log.info("开瓶违约：已经存在违约处理记录, scanId is [{}]", scanId);
                list.forEach(exceptionRecordModel -> {
                    exceptionRecordModel.setFenceStatus(1);
                    exceptionRecordModel.setUpdateTime(new Date());
                    exceptionRecordModel.setRemark("开瓶违约：处理成功");
                    activityRewardExceptionRecordService.updateById(exceptionRecordModel);
                });
                continue;
            }
            //违约记录
            FenceViolateOpenBottleAreaRecordModel fenceModel = new FenceViolateOpenBottleAreaRecordModel();
            fenceModel.setScanId(scanId.longValue());
            fenceModel.setDealStatus(0);
            fenceModel.setAppealStatus(0);
            if (CollectionUtils.isEmpty(scanMap.get(scanId))) {
                continue;
            }
            //查询抽奖记录
            ConsumerScanDetailCommonModel model = consumerScanDetailCommonDao.selectById(scanId);
            if (Objects.isNull(model)) {
                log.info("开瓶违约：未查询到抽奖信息, scanId is [{}]", scanId);
                scanMap.get(scanId).forEach(exceptionRecordModel -> {
                    exceptionRecordModel.setFenceStatus(1);
                    exceptionRecordModel.setRemark("异地违约：未查询到抽奖信息");
                    activityRewardExceptionRecordService.updateById(exceptionRecordModel);
                });
                continue;
            }
            fenceModel.setGoodsCode(model.getGoodsCode());
            fenceModel.setGoodsName(model.getGoodsName());
            fenceModel.setDrawId(model.getDrawId().longValue());
            fenceModel.setDrawName(model.getNickname());
            fenceModel.setQrCode(model.getQrcode());
            fenceModel.setBoxCode(model.getCodeXiang());
            if (StringUtils.isBlank(fenceModel.getBoxCode())) {
                ExtendDataBean extendDataBean = JSON.parseObject(scanMap.get(scanId).get(0).getExtendData(), ExtendDataBean.class);
                if (extendDataBean != null) {
                    String boxCode = extendDataBean.getBoxCode();
                    fenceModel.setBoxCode(boxCode);
                }
            }
            fenceModel.setInCode(model.getCodeIn());
            fenceModel.setDrawTime(model.getDrawTime());
            fenceModel.setDrawArea(model.getDrawRegionName());
            fenceModel.setUpdateTime(new Date());
            fenceModel.setIsDelete(0);
            fenceModel.setProvince(model.getProvince());
            fenceModel.setCity(model.getCity());
            fenceModel.setDistrict(model.getDistrict());
            //添加违约类型 一个码 不同奖励 对应的违约类型应该是一样的 0是省外 1是省内异地
            fenceModel.setFenceType(scanMap.get(scanId).get(0).getFenceType());
            //合同设置为异地开瓶时的合同 不用码信息里面的合同
            model.setContractNo(scanMap.get(scanId).get(0).getContractCode());

            //查询开瓶的终端信息
            ShopModel shopInfo = shopDao.getById(model.getShopId());
            if (Objects.isNull(shopInfo)) {
                log.info("开瓶违约：未查询到入库终端信息, scanId is [{}]", scanId);
                scanMap.get(scanId).forEach(exceptionRecordModel -> {
                    exceptionRecordModel.setFenceStatus(1);
                    exceptionRecordModel.setRemark("异地违约：未查询到入库终端信息");
                    activityRewardExceptionRecordService.updateById(exceptionRecordModel);
                });
                continue;
            }

            //查询开瓶码 是否在白名单之中 如果在白名单 此条位于无需处理 并把异常奖励记录放回到正常奖励记录里面
            //白名单存在箱码和瓶码 如果是箱码直接用箱码进行对比，整箱都在白名单
            //如果是瓶码 需要判断是否在白名单中
            List<String> codes = new ArrayList<>();
            if (StringUtils.isNotBlank(model.getCodeXiang())) {
                codes.add(model.getCodeXiang());
            }

            if (StringUtils.isNotBlank(model.getCodeIn())) {
                List<String> collect = Arrays.stream(model.getCodeIn().split(",")).collect(Collectors.toList());
                codes.addAll(collect);
            }

            //查询违约经销商信息
            CloudDealerInfoModel fenceDealerInfo = dealerInfoCommonDao.getDealerInfoByDealerCode(model.getDealerCode());
            if (Objects.isNull(fenceDealerInfo)) {
                log.info("开瓶违约：未查询到违约经销商信息, scanId is [{}]", scanId);
                scanMap.get(scanId).forEach(exceptionRecordModel -> {
                    exceptionRecordModel.setFenceStatus(1);
                    exceptionRecordModel.setRemark("异地违约：未查询到违约经销商信息");
                    activityRewardExceptionRecordService.updateById(exceptionRecordModel);
                });
                continue;
            }
            // TODO: 2024/12/25 这里的经销商编码是从consumerScanDetail中获取的，是否需要从exceptionRecord中获取？两者有可能不一致。
            fenceModel.setViolateDealerCode(fenceDealerInfo.getDealerCode());

            //查询违约经销商合同类型
            if (StringUtils.isBlank(model.getContractNo())) {
                log.info("开瓶违约：未查询到违约经销商合同, scanId is [{}]", scanId);
                scanMap.get(scanId).forEach(exceptionRecordModel -> {
                    exceptionRecordModel.setFenceStatus(1);
                    exceptionRecordModel.setRemark("异地违约：未查询到违约经销商合同");
                    activityRewardExceptionRecordService.updateById(exceptionRecordModel);
                });
                continue;
            }
            Integer fenceContractType = dealerContractRelCommonDao.selectContractType(model.getContractNo());
            if (Objects.isNull(fenceContractType)) {
                log.info("开瓶违约：未查询到违约经销商合同类型信息, scanId is [{}]", scanId);
                scanMap.get(scanId).forEach(exceptionRecordModel -> {
                    exceptionRecordModel.setFenceStatus(1);
                    exceptionRecordModel.setRemark("异地违约：未查询到违约经销商合同类型信息");
                    activityRewardExceptionRecordService.updateById(exceptionRecordModel);
                });
                continue;
            }
            // TODO: 2024/12/25 这里的合同编码是从consumerScanDetail中获取的，是否需要从exceptionRecord中获取？两者有可能不一致。
            fenceModel.setViolateContractCode(model.getContractNo());
            fenceModel.setViolateContractType(fenceContractType);
            //白名单判断放在开瓶处理逻辑 这里不在进行判断
//            if(codes != null && codes.size() >0 ){
//                List<TerminalWhiteRuleCodeModel> terminalWhiteRuleCodeModels = whiteRuleCodeDao.existCode(codes);
//                if (terminalWhiteRuleCodeModels != null && terminalWhiteRuleCodeModels.size() > 0) {
//                    // 1.存在白名单中
//                    log.info("开瓶码：{} 对应的箱码：{}，或对应的瓶码{}，存在于白名单中,不能在执行处罚", model.getCodeXiang(), model.getCodeXiang(), model.getCodeIn());
//                    // 2.记录在异地处罚违约中 状态是无需处理
//                    fenceModel.setDealStatus(3);
//                    fenceModel.setRemark("异地违约：开瓶码存在于白名单中,对应的箱码：" + model.getCodeXiang() + "，对应的瓶码：" + model.getCodeIn());
//                    fenceViolateOpenBottleAreaRecordDao.insert(fenceModel);
//                    // 3.异常表记录放在正常记录表中
//                    scanMap.get(scanId).forEach(exceptionRecordModel -> {
//                        exceptionRecordModel.setFenceStatus(1);
//                        exceptionRecordModel.setRemark("异地违约：开瓶码存在于白名单中");
//                        exceptionRecordModel.setIsDelete(1);//删除
//                        exceptionRecordModel.setApproveStatus(1);//转为正常
//                        activityRewardExceptionRecordService.updateById(exceptionRecordModel);
//
//                        ActivityRewardRecordModel activityRewardRecordModel = new ActivityRewardRecordModel();
//                        BeanUtils.copyProperties(exceptionRecordModel, activityRewardRecordModel);
//                        activityRewardRecordModel.setId(null);
//                        activityRewardRecordModel.setSendMsg(null);
//                        if (exceptionRecordModel.getRewardId() != null) {
//                            activityRewardRecordModel.setRewardId(exceptionRecordModel.getRewardId().longValue());
//                        }
//                        if (exceptionRecordModel.getIsMember() != null) {
//                            activityRewardRecordModel.setIsMember(exceptionRecordModel.getIsMember() + "");
//                        }
//                        if (activityRewardRecordModel.getIntegral() == null) {
//                            activityRewardRecordModel.setIntegral(new BigDecimal(0));
//                        }
//                        activityRewardRecordModel.setSendStatus(0);
//                        activityRewardRecordModel.setIsDelete(0);
//                        activityRewardRecordModel.setCreateTime(new Date());
//                        activityRewardRecordService.save(activityRewardRecordModel);
//                    });
//                    continue;
//                }
//            }

            // 获取是否为高端酒
            Boolean isHighEndWine = null;

            //获取收货记录
            Integer scanDetailId = 0;
            for (ActivityRewardExceptionRecordModel fenceRecord : scanMap.get(scanId)) {
                fenceRecord.setExtendDataBean(JSON.parseObject(fenceRecord.getExtendData(), ExtendDataBean.class));
                if (Objects.nonNull(fenceRecord.getExtendDataBean().getScanDetailId())) {
                    scanDetailId = fenceRecord.getExtendDataBean().getScanDetailId();
                }
                if (Objects.isNull(isHighEndWine) && Objects.nonNull(fenceRecord.getExtendDataBean().getLiquorGrade())) {
                    isHighEndWine = fenceRecord.getExtendDataBean().getLiquorGrade().equals(BusinessLineEnum.HIGH_END.getValue());
                }
            }
            if (Objects.isNull(isHighEndWine)) {
                try {
                    // 根据合同业务线判断是否为高端酒(SZYXPT-1675)
                    BusinessLineEnum businessLine = dealerContractRelCommonService.getBusinessLine(model.getContractNo());
                    log.info("合同[{}]的业务线判断结果：[{}]", model.getContractNo(), businessLine);

                    if (Objects.isNull(businessLine) || BusinessLineEnum.isNotAllowed(businessLine)) {
                        String errorMsg = String.format("合同[%s]根据类型无法确定高端/国标业务线，无法进行处理", model.getContractNo());
                        log.warn(errorMsg);
                        updateExceptionRecords(scanMap.get(scanId), errorMsg);
                        continue;
                    }

                    isHighEndWine = BusinessLineEnum.HIGH_END.equals(businessLine);
                    log.info("合同[{}]业务线判断完成，是否为高端酒: {}", model.getContractNo(), isHighEndWine);
                } catch (BusinessException e) {
                    String errorMsg = String.format("获取合同[%s]业务线信息失败: %s", model.getContractNo(), e.getMessage());
                    log.error(errorMsg, e);
                    updateExceptionRecords(scanMap.get(scanId), errorMsg);
                    continue;
                }
            }

            if (scanDetailId.equals(0)) {
                String errorMsg = "未查询到收货记录";
                log.warn("开瓶违约：{}，scanId: [{}]", errorMsg, scanId);
                updateExceptionRecords(scanMap.get(scanId), errorMsg);
                continue;
            }

            //违约处罚和补偿处理
            //查询违约处罚奖励配置
            List<FenceViolationOfAgreementSettingModel> setingModelList = fenceViolationOfAgreementSettingDao.getFenceAreaSettingRecord(fenceContractType, model.getGoodsCode(), fenceModel.getFenceType());
            // 无违约处罚奖励配置，也能继续走下去。
            if (CollectionUtils.isEmpty(setingModelList)) {
                log.info("开瓶违约：未查询到违约处罚奖励配置, scanId is [{}]", scanId);
                scanMap.get(scanId).forEach(exceptionRecordModel -> {
                    exceptionRecordModel.setFenceStatus(1);
                    exceptionRecordModel.setRemark("开瓶违约：未查询到违约处罚奖励配置");
                    activityRewardExceptionRecordService.updateById(exceptionRecordModel);
                });
            }

            FenceViolationOfAgreementSettingModel settingModel = null;
            if (CollectionUtils.isNotEmpty(setingModelList)) {
                settingModel = setingModelList.get(0);
            }

            // 查询是否已经存在违约处理记录
            List<FenceViolateOpenBottleAreaRecordModel> existedFenceRecordList  = lambdaQuery()
                    .eq(FenceViolateOpenBottleAreaRecordModel::getScanId, scanId)
                    .eq(FenceViolateOpenBottleAreaRecordModel::getAppealStatus, 0)
                    .eq(FenceViolateOpenBottleAreaRecordModel::getIsDelete, IsDelete.DELETE_FALSE.getCode())
                    .list();

            if (CollUtil.isNotEmpty(existedFenceRecordList)) {
                List<Long> ids = existedFenceRecordList.stream().map(FenceViolateOpenBottleAreaRecordModel::getId).collect(Collectors.toList());
                log.info("开瓶违约：已经存在违约处理记录, scanId is [{}], 违约记录id为:[{}]", scanId, ids);
                //更新违约记录状态
                scanMap.get(scanId).forEach(exceptionRecordModel -> {
                    log.info("开瓶违约：已经存在违约处理记录, scanId is [{}], 当前的exception数据为:[{}]", scanId, JSON.toJSONString(exceptionRecordModel));
                    exceptionRecordModel.setFenceStatus(1);
                    exceptionRecordModel.setUpdateTime(new Date());
                    String remark = exceptionRecordModel.getRemark() + "; 开瓶违约：已经存在违约处理记录";
                    exceptionRecordModel.setRemark(org.apache.commons.lang3.StringUtils.abbreviate(remark, 1900));
                    activityRewardExceptionRecordService.updateById(exceptionRecordModel);
                });
                continue;
            }

            fenceViolateOpenBottleAreaRecordDao.insert(fenceModel);
            //查询违约经销商的动销奖励
            List<ActivityRewardRecordModel> dealerSalesRewardList = activityRewardRecordDao.findRewardByScanDetailId(scanDetailId, 1);
            //查询违约终端的动销奖励
            List<ActivityRewardRecordModel> terminalSalesRewardList = activityRewardRecordDao.findRewardByScanDetailId(scanDetailId, 5);
            //查询违约分销商的动销奖励
            List<ActivityRewardRecordModel> distributionSalesRewardList = activityRewardRecordDao.findRewardByScanDetailId(scanDetailId, 3);
            //查询违约经销商的开瓶奖励
            List<ActivityRewardExceptionRecordModel> dealerOpenRewardList = activityRewardExceptionRecordDao.findRewardByScanId(fenceModel.getScanId().intValue(), 1);
            // 获取违约经销商的开瓶普通奖励
            dealerOpenRewardList = dealerOpenRewardList.stream().filter(item -> Objects.equals(item.getRewardSubType(), ActivityRewardSubTypeEnum.OPEN_BOTTLE_NORMAL_REWARD.getCode())).collect(Collectors.toList());
            //查询违约终端的开瓶奖励
            List<ActivityRewardExceptionRecordModel> terminalOpenRewardList = activityRewardExceptionRecordDao.findRewardByScanId(fenceModel.getScanId().intValue(), 5);
           // 获取违约终端的开瓶普通奖励
            terminalOpenRewardList = terminalOpenRewardList.stream().filter(item -> Objects.equals(item.getRewardSubType(), ActivityRewardSubTypeEnum.OPEN_BOTTLE_NORMAL_REWARD.getCode())).collect(Collectors.toList());
            //查询违约分销商的开瓶奖励
            List<ActivityRewardExceptionRecordModel> distributionOpenRewardList = activityRewardExceptionRecordDao.findRewardByScanId(fenceModel.getScanId().intValue(), 3);
            //查询违约分销商的开瓶普通奖励
            distributionOpenRewardList = distributionOpenRewardList.stream().filter(item -> Objects.equals(item.getRewardSubType(), ActivityRewardSubTypeEnum.OPEN_BOTTLE_NORMAL_REWARD.getCode())).collect(Collectors.toList());
            FenceRewardInfoModel fenceRewardInfoModel = new FenceRewardInfoModel(dealerSalesRewardList, terminalSalesRewardList, distributionSalesRewardList, dealerOpenRewardList, terminalOpenRewardList, distributionOpenRewardList);

            BusinessLineEnum businessLineEnum = isHighEndWine ? BusinessLineEnum.HIGH_END : BusinessLineEnum.GB;
            log.info("异地违约商品的等级为：{}", businessLineEnum.getDesc());
            boolean handleResult;

            try {
                handleResult = FenceViolationStrategyFactory.getInvokeHandler(businessLineEnum).handleFenceViolation(scanId, fenceModel, fenceRewardInfoModel, settingModel, model, fenceDealerInfo, fenceContractType, scanMap);
            } catch (Exception e) {
                log.warn("违约处理异常, 异常信息为:[{}]", e.getMessage(), e);
                handleResult = false;
                scanMap.get(scanId).forEach(exceptionRecordModel -> {
                    exceptionRecordModel.setFenceStatus(1);
                    String remark = "区域区隔处理发生异常，合同编码: " + model.getContractNo() + ", 商品编码: " + model.getGoodsCode() + ", 异常信息: " + e.getMessage();
                    exceptionRecordModel.setRemark(remark.substring(0, Math.min(remark.length(), 1900)));
                    activityRewardExceptionRecordService.updateById(exceptionRecordModel);
                });
            }

            if (!handleResult) {
                log.info("开瓶违约处理返回false，scanId is [{}]", scanId);
                continue;
            }

            //更新违约记录状态
            scanMap.get(scanId).forEach(exceptionRecordModel -> {
                log.info("开瓶违约处理完成，scanId is [{}], exceptionId is [{}]", scanId, exceptionRecordModel.getId());
                exceptionRecordModel.setFenceStatus(1);
                exceptionRecordModel.setUpdateTime(new Date());
                exceptionRecordModel.setRemark("开瓶违约：处理成功" + "--" + exceptionRecordModel.getRemark());
                activityRewardExceptionRecordService.updateById(exceptionRecordModel);
                log.info("开瓶违约处理完成，更新状态完成，scanId is [{}], exceptionId is [{}]", scanId, exceptionRecordModel.getId());
            });
        }
    }

    @Override
    public FenceViolateOpenBottleAreaRecordResp queryAppealViolateOpenBottleAreaRecordById(Integer id) {
        //查询异地违约基本信息
        FenceViolateOpenBottleAreaRecordResp resp = this.findById(id);

        //扣除补偿奖励
        resp.setDeductRewardList(this.appealViolateDealerRewardList(resp));
        //扣留奖励
        resp.setReserveRewardList(this.reserveRewardList(resp));
        return resp;
    }

    @Override
    public void addAppeal(FenceViolationOfAgreementAppealModel model) {
        FenceViolateOpenBottleAreaRecordModel fenceModel = fenceViolateOpenBottleAreaRecordDao.selectById(model.getViolationOfAgreementInfoId());
        if (Objects.isNull(fenceModel)) {
            throw new BusinessException("未查询到违约记录");
        }
        if (fenceModel.getAppealStatus() != 0) {
            throw new BusinessException("违约记录已处理");
        }
        FenceViolationOfAgreementAppealModel appealModel = fenceViolationOfAgreementAppealDao.selectByFenceId(model.getViolationOfAgreementInfoId());
        if (Objects.nonNull(appealModel)) {
            throw new BusinessException("违约记录已处理");
        }
        //查询经销商是否配置申诉权限
        ActivityExceptionComplaintConfigResp config = activityExceptionComplaintConfigDao.selectByDealerCode(fenceModel.getViolateDealerCode());
        if (Objects.isNull(config)) {
            throw new BusinessException("经销商未配置申诉权限");
        }
        if (config.getUseStatus() != 0) {
            throw new BusinessException("经销商申诉权限已停用");
        }
        //更新申诉状态
        fenceModel.setAppealStatus(1);
        fenceViolateOpenBottleAreaRecordDao.updateById(fenceModel);
        model.setViolationOfAgreementInfoId(fenceModel.getId().intValue());
        model.setCreateTime(new Date());
        model.setIsDelete(IsDelete.DELETE_FALSE.getCode());
        model.setStatus(0);
        model.setAppealUser(model.getCreateUser());
        model.setAppealUserName(model.getCreateUserName());
        //插入审批记录
        fenceViolationOfAgreementAppealDao.insert(model);
    }

    @Override
    public BatchFenceViolationAppealResp batchAddAppeal(BatchAppealServiceReq batchAppealReq) {
        log.info("批量申诉开始, 违约记录ID列表：{}, 申诉人：{}", batchAppealReq.getViolationIds(), batchAppealReq.getCreateUserName());
        
        BatchFenceViolationAppealResp response = new BatchFenceViolationAppealResp();
        response.setSuccessIds(new ArrayList<>());
        response.setFailedItems(new ArrayList<>());
        
        // 基础参数校验
        if (CollectionUtils.isEmpty(batchAppealReq.getViolationIds())) {
            throw new BusinessException("违约记录ID列表不能为空");
        }
        if (batchAppealReq.getViolationIds().size() > 100) {
            throw new BusinessException("批量申诉数量不能超过100条");
        }
        if (StringUtils.isBlank(batchAppealReq.getReason())) {
            throw new BusinessException("申诉说明不能为空");
        }
        if (StringUtils.isBlank(batchAppealReq.getAppealFileUrl())) {
            throw new BusinessException("申诉附件不能为空");
        }
        
        Date currentTime = new Date();
        
        // 1. 批量查询违约记录 - 使用LambdaQuery
        List<FenceViolateOpenBottleAreaRecordModel> fenceRecords = lambdaQuery()
                .in(FenceViolateOpenBottleAreaRecordModel::getId, batchAppealReq.getViolationIds())
                .list();
        
        // 构建ID到记录的映射
        Map<Integer, FenceViolateOpenBottleAreaRecordModel> fenceRecordMap = fenceRecords.stream()
                .collect(Collectors.toMap(record -> record.getId().intValue(), record -> record));
        
        // 2. 批量查询现有申诉记录 - 使用LambdaQuery  
        LambdaQueryWrapper<FenceViolationOfAgreementAppealModel> appealQuery = Wrappers.lambdaQuery();
        appealQuery.in(FenceViolationOfAgreementAppealModel::getViolationOfAgreementInfoId, batchAppealReq.getViolationIds())
                .eq(FenceViolationOfAgreementAppealModel::getIsDelete, IsDelete.DELETE_FALSE.getCode());
        List<FenceViolationOfAgreementAppealModel> existingAppeals = fenceViolationOfAgreementAppealDao.selectList(appealQuery);
        Set<Integer> existingAppealViolationIds = existingAppeals.stream()
                .map(FenceViolationOfAgreementAppealModel::getViolationOfAgreementInfoId)
                .collect(Collectors.toSet());
        
        // 3. 收集所有需要查询的经销商编码（去重）
        Set<String> dealerCodes = fenceRecords.stream()
                .filter(record -> StringUtils.isNotBlank(record.getViolateDealerCode()))
                .map(FenceViolateOpenBottleAreaRecordModel::getViolateDealerCode)
                .collect(Collectors.toSet());
        
        // 4. 批量查询经销商申诉权限配置 - 使用LambdaQuery
        Map<String, ActivityExceptionComplaintConfigResp> dealerConfigMap = new HashMap<>();
        if (!dealerCodes.isEmpty()) {
            LambdaQueryWrapper<ActivityExceptionComplaintConfigModel> configQuery = Wrappers.lambdaQuery();
            configQuery.in(ActivityExceptionComplaintConfigModel::getDealerCode, dealerCodes)
                    .eq(ActivityExceptionComplaintConfigModel::getDeleteStatus, 0);
            List<ActivityExceptionComplaintConfigModel> configs = activityExceptionComplaintConfigDao.selectList(configQuery);
            
            // 转换为响应对象
            dealerConfigMap = configs.stream()
                    .collect(Collectors.toMap(
                        ActivityExceptionComplaintConfigModel::getDealerCode, 
                        config -> {
                            ActivityExceptionComplaintConfigResp resp = new ActivityExceptionComplaintConfigResp();
                            resp.setDealerCode(config.getDealerCode());
                            resp.setUseStatus(config.getUseStatus());
                            return resp;
                        }
                    ));
        }
        
        // 5. 数据验证和分批处理 - 逐个处理每条记录，确保单条错误不影响其他记录
        List<Integer> validViolationIds = new ArrayList<>();
        List<FenceViolationOfAgreementAppealModel> appealsToInsert = new ArrayList<>();
        
        for (Integer violationId : batchAppealReq.getViolationIds()) {
            try {
                // 检查违约记录是否存在
                FenceViolateOpenBottleAreaRecordModel fenceRecord = fenceRecordMap.get(violationId);
                if (Objects.isNull(fenceRecord)) {
                    response.getFailedItems().add(new BatchFenceViolationAppealResp.FailedAppealItem(violationId, "未查询到违约记录"));
                    continue;
                }
                
                // 检查申诉状态
                if (fenceRecord.getAppealStatus() != 0) {
                    response.getFailedItems().add(new BatchFenceViolationAppealResp.FailedAppealItem(violationId, "违约记录已处理"));
                    continue;
                }
                
                // 检查是否已有申诉记录
                if (existingAppealViolationIds.contains(violationId)) {
                    response.getFailedItems().add(new BatchFenceViolationAppealResp.FailedAppealItem(violationId, "该记录已有申诉"));
                    continue;
                }
                
                // 检查经销商申诉权限
                ActivityExceptionComplaintConfigResp config = dealerConfigMap.get(fenceRecord.getViolateDealerCode());
                if (Objects.isNull(config)) {
                    response.getFailedItems().add(new BatchFenceViolationAppealResp.FailedAppealItem(violationId, "经销商未配置申诉权限"));
                    continue;
                }
                if (config.getUseStatus() != 0) {
                    response.getFailedItems().add(new BatchFenceViolationAppealResp.FailedAppealItem(violationId, "经销商申诉权限已停用"));
                    continue;
                }
                
                // 验证通过，加入待处理列表
                validViolationIds.add(violationId);
                
                // 准备插入的申诉记录
                FenceViolationOfAgreementAppealModel appealToInsert = new FenceViolationOfAgreementAppealModel();
                appealToInsert.setViolationOfAgreementInfoId(violationId);
                appealToInsert.setReason(batchAppealReq.getReason());
                appealToInsert.setAppealFileUrl(batchAppealReq.getAppealFileUrl());
                appealToInsert.setCreateTime(currentTime);
                appealToInsert.setCreateUser(batchAppealReq.getCreateUser());
                appealToInsert.setCreateUserName(batchAppealReq.getCreateUserName());
                appealToInsert.setCompanyId(batchAppealReq.getCompanyId());
                appealToInsert.setAppealUserTel(batchAppealReq.getAppealUserTel());
                appealToInsert.setIsDelete(IsDelete.DELETE_FALSE.getCode());
                appealToInsert.setStatus(0); // 申诉中
                appealToInsert.setAppealUser(batchAppealReq.getCreateUser());
                appealToInsert.setAppealUserName(batchAppealReq.getCreateUserName());
                appealsToInsert.add(appealToInsert);
                
                log.debug("批量申诉验证通过，违约记录ID：{}", violationId);
                
            } catch (Exception e) {
                log.error("批量申诉处理违约记录ID：{} 时发生异常：{}", violationId, e.getMessage(), e);
                response.getFailedItems().add(new BatchFenceViolationAppealResp.FailedAppealItem(violationId, "处理异常：" + e.getMessage()));
            }
        }
        
        // 6. 分批执行数据库操作 - 使用独立事务，确保部分失败不影响其他记录
        if (!validViolationIds.isEmpty()) {
            try {
                // 通过AOP代理调用事务方法，确保事务注解生效
                FenceViolateOpenBottleAreaRecordServiceImpl proxy = 
                    (FenceViolateOpenBottleAreaRecordServiceImpl) AopContext.currentProxy();
                proxy.processBatchAppealOperations(validViolationIds, appealsToInsert, response);
            } catch (Exception e) {
                log.error("批量申诉数据库操作失败：{}", e.getMessage(), e);
                // 操作失败，将所有ID加入失败列表
                validViolationIds.forEach(violationId -> 
                    response.getFailedItems().add(new BatchFenceViolationAppealResp.FailedAppealItem(
                        violationId, "数据库操作失败：" + e.getMessage()))
                );
            }
        }
        
        log.info("批量申诉完成, 成功数量：{}, 失败数量：{}", response.getSuccessIds().size(), response.getFailedItems().size());
        return response;
    }

    /**
     * 分批处理申诉操作，使用独立事务确保单条记录错误不影响其他记录
     * 注意：此方法需要通过AOP代理调用才能使事务注解生效
     */
    @Transactional(rollbackFor = Exception.class)
    public void processBatchAppealOperations(List<Integer> validViolationIds, 
                                           List<FenceViolationOfAgreementAppealModel> appealsToInsert,
                                           BatchFenceViolationAppealResp response) {
        
        // 批量更新违约记录申诉状态
        if (!validViolationIds.isEmpty()) {
            boolean updateSuccess = lambdaUpdate()
                    .set(FenceViolateOpenBottleAreaRecordModel::getAppealStatus, 1)
                    .set(FenceViolateOpenBottleAreaRecordModel::getUpdateTime, new Date())
                    .in(FenceViolateOpenBottleAreaRecordModel::getId, validViolationIds)
                    .update();
            
            if (!updateSuccess) {
                throw new BusinessException("批量更新违约记录申诉状态失败");
            }
            log.info("批量更新违约记录申诉状态完成，数量：{}", validViolationIds.size());
        }
        
        // 批量插入申诉记录 - 使用XML实现
        if (!appealsToInsert.isEmpty()) {
            int insertCount = fenceViolationOfAgreementAppealDao.batchInsert(appealsToInsert);
            if (insertCount != appealsToInsert.size()) {
                throw new BusinessException("批量插入申诉记录失败，期望插入" + appealsToInsert.size() + "条，实际插入" + insertCount + "条");
            }
            log.info("批量插入申诉记录完成，数量：{}", insertCount);
        }
        
        // 操作成功，将所有ID加入成功列表
        response.getSuccessIds().addAll(validViolationIds);
        log.info("批量申诉事务操作完成，成功处理数量：{}", validViolationIds.size());
    }

    @Override
    public void dealFenceTypeData() {
        //1. 查询出 类型为异地 且异地类型为空的数据
        List<ActivityRewardExceptionRecordModel> exceptionRecordModels = fenceViolateOpenBottleAreaRecordDao.selectNoDealFenceType();
        if (Objects.isNull(exceptionRecordModels)) {
            log.info("查询不到异地类型为空的数据。");
            return;
        }
        //2. 根据记录异地信息 判断出是省内异地还是省外异地 首先在数据库查询 高端酒会员开瓶 不会进入异地 在收货阶段就会进入到异常数据
        for (ActivityRewardExceptionRecordModel exceptionRecordModel : exceptionRecordModels) {
            String sendMsg = exceptionRecordModel.getSendMsg();
            if (StringUtils.isBlank(sendMsg)) {
                //这部分数据应该很少
                log.info("未记录异地信息,无法判断是异地类型，先将数据设置为无法判断");
                exceptionRecordModel.setFenceType(-1);
                activityRewardExceptionRecordDao.updateById(exceptionRecordModel);
                continue;
            }

            String openArea = exceptionRecordModel.getDrawRegionName();
            String openProvince = exceptionRecordModel.getProvince();
            String openCity = exceptionRecordModel.getCity();
            //省市区 不从异常记录表取 从消费者开瓶信息里面取
            String dealerAuthorizedArea = sendMsg.substring(sendMsg.indexOf("经销商授权区域:") + 1);
            log.info("消费者开瓶区域：{}，经销商授权区域：{}", openArea, dealerAuthorizedArea);
            // 解析出授权区域
            List<String> dealerAuthList = new ArrayList<>();
            if (StringUtils.isNotBlank(dealerAuthorizedArea)) {
                String[] split = dealerAuthorizedArea.split("/");
                dealerAuthList = Arrays.asList(split);
                List<String> collect = dealerAuthList.stream().filter(s -> s.contains(openProvince)).collect(Collectors.toList());
                if (collect.size() > 0) {
                    //省内异地
                    exceptionRecordModel.setFenceType(1);
                } else {
                    //省外异地
                    exceptionRecordModel.setFenceType(0);
                }
                //3. 更新异地类型
                activityRewardExceptionRecordDao.updateById(exceptionRecordModel);
            } else {
                log.info("未查询到授权区域,无法判断是异地类型，先将数据设置为无法判断");
                exceptionRecordModel.setFenceType(-1);
                activityRewardExceptionRecordDao.updateById(exceptionRecordModel);
            }
        }
    }

    public List<RewardResp> dxRewardList(FenceViolateOpenBottleAreaRecordResp resp) {
        List<ActivityRewardExceptionRecordModel> activityRewardExceptionRecordModels = activityRewardExceptionRecordDao.findReseveRewardByRecordId(resp.getId());
        Set<Integer> detailIds = new HashSet<>();
        for (ActivityRewardExceptionRecordModel model : activityRewardExceptionRecordModels) {
            ExtendDataBean extendDataBean = JSONObject.parseObject(model.getExtendData(), ExtendDataBean.class);
            detailIds.add(extendDataBean.getScanDetailId());
        }
        List<ActivityRewardRecordModel> ActivityRewardRecordModelList = activityRewardRecordDao.findDxRewardByDetailIds(new ArrayList<>(detailIds));
        List<RewardResp> dxRewards = new ArrayList<>();
        for (ActivityRewardRecordModel model : ActivityRewardRecordModelList) {
            ExtendDataBean extendDataBean = JSONObject.parseObject(model.getExtendData(), ExtendDataBean.class);
            RewardResp resp1 = new RewardResp();
            resp1.setShowDate(model.getCreateTime());
            resp1.setRewardName(ActivityType.getName(model.getActivityType()));
            resp1.setRewardTypeName(RewardType.getName(model.getRewardType()));
            resp1.setRewardContent(extendDataBean.getRewardContent());
            resp1.setName(extendDataBean.getShopName());
            resp1.setRoleName(extendDataBean.getRoleName());
            resp1.setDealerCode(extendDataBean.getDealerCode());
            dxRewards.add(resp1);
        }
        return dxRewards;
    }

    public List<DeliveryModel> queryDeliveryList(FenceViolateOpenBottleAreaRecordResp resp) {
        // 组装收货发货记录
        List<ActivityRewardExceptionRecordModel> activityRewardExceptionRecordModels = activityRewardExceptionRecordDao.findReseveRewardByRecordId(resp.getId());
        Set<Integer> detailIds = new HashSet<>();
        for (ActivityRewardExceptionRecordModel model : activityRewardExceptionRecordModels) {
            ExtendDataBean extendDataBean = JSONObject.parseObject(model.getExtendData(), ExtendDataBean.class);
            detailIds.add(extendDataBean.getScanDetailId());
        }
        List<DeliveryModel> deliveryList = activityRewardRecordDao.deliveryList(new ArrayList<>(detailIds));

        List<DeliveryModel> deliveryModels = new ArrayList<>();
        for (DeliveryModel deliveryModel : deliveryList) {
            //一条拆分出收发货两条数据
            DeliveryModel sendModel = new DeliveryModel();
            sendModel.setDealerCode(deliveryModel.getSenderCode()); // 发货方编码
            sendModel.setDealerName(deliveryModel.getSenderName()); // 发货方名称
            sendModel.setOrderCode(deliveryModel.getSendNo());      // 发货订单
            sendModel.setDeliveryTime(deliveryModel.getSendDate()); // 发货日期
            if (deliveryModel.getOrderCode().startsWith("GH")) {
                sendModel.setSendType(1); //订货订单
            } else {
                sendModel.setSendType(2); //直发订单
            }
            sendModel.setDealMsg("发货");
            DeliveryModel receiveModel = new DeliveryModel();
            receiveModel.setDealerCode(deliveryModel.getDealerCode()); //收货方编码
            receiveModel.setDealerName(deliveryModel.getDealerName()); //收货方名称
            receiveModel.setOrderCode(deliveryModel.getOrderCode());   //收货方订单
            receiveModel.setDeliveryTime(deliveryModel.getDeliveryTime()); //收货日期
            receiveModel.setContractCode(deliveryModel.getContractCode());//收货合同编号
            receiveModel.setDeliveryAddress(deliveryModel.getDeliveryAddress());//收货地址
            receiveModel.setDealMsg("收货");

            //去除名字里面带团购客户的发货方
            if (deliveryModel.getSenderName().contains("团购客户")) {
                continue;
            }
            deliveryModels.add(sendModel);
            deliveryModels.add(receiveModel);
        }
        return deliveryModels;
    }

    //已发奖励记录
    public List<RewardResp> openRewardList(FenceViolateOpenBottleAreaRecordResp resp) {
        //根据记录表id查询扣留奖励记录
        List<ActivityRewardRecordModel> activityRewardRecordModels = activityRewardRecordDao.findOpenRewardByRecordId(resp.getId());
        List<RewardResp> reserveRewards = new ArrayList<>();
        for (ActivityRewardRecordModel model : activityRewardRecordModels) {
            ExtendDataBean extendDataBean = JSONObject.parseObject(model.getExtendData(), ExtendDataBean.class);
            RewardResp resp1 = new RewardResp();
            resp1.setShowDate(model.getCreateTime());
            resp1.setRewardName(ActivityType.getName(model.getActivityType()));
            resp1.setRewardTypeName(RewardType.getName(model.getRewardType()));
            resp1.setRewardContent(extendDataBean.getRewardContent());
            resp1.setName(extendDataBean.getShopName());
            resp1.setRoleName(extendDataBean.getRoleName());
            resp1.setDealerCode(extendDataBean.getDealerCode());
            reserveRewards.add(resp1);
        }
        return reserveRewards;
    }


    //已扣留奖励记录
    public List<RewardResp> reserveRewardList(FenceViolateOpenBottleAreaRecordResp resp) {
        //根据记录表id查询扣留奖励记录
        List<ActivityRewardExceptionRecordModel> activityRewardExceptionRecordModels = activityRewardExceptionRecordDao.findReseveRewardByRecordId(resp.getId());
        activityRewardExceptionRecordModels = activityRewardExceptionRecordModels.stream().filter(r -> r.getIsDelete() == 0).collect(Collectors.toList());
        List<RewardResp> reserveRewards = new ArrayList<>();
        for (ActivityRewardExceptionRecordModel model : activityRewardExceptionRecordModels) {
            ExtendDataBean extendDataBean = JSONObject.parseObject(model.getExtendData(), ExtendDataBean.class);
            RewardResp resp1 = new RewardResp();
            resp1.setShowDate(model.getCreateTime());
            resp1.setRewardName(ActivityType.getName(model.getActivityType()));
            resp1.setRewardTypeName(RewardType.getName(model.getRewardType()));
            if (Objects.equals(RewardType.SEND_SCORE_TYPE.getCode(), model.getRewardType())) {
                BigDecimal integral = model.getIntegral();
                if (Objects.equals(resp.getOpenBottleRewardFlag() ,1)) {
                    integral = integral.multiply(BigDecimal.ONE.subtract(Optional.ofNullable(resp.getOpenBottleRewardRate()).orElse(BigDecimal.ZERO)));
                }
                resp1.setRewardContent(integral.toPlainString() + "积分");
            } else {
                resp1.setRewardContent(extendDataBean.getRewardContent());
            }
            resp1.setName(extendDataBean.getShopName());
            resp1.setRoleName(extendDataBean.getRoleName());
            resp1.setDealerCode(extendDataBean.getDealerCode());
            reserveRewards.add(resp1);
        }
        return reserveRewards;
    }


    //已扣除奖励记录
    public List<RewardResp> deductRewardList(FenceViolateOpenBottleAreaRecordResp resp) {
        //根据记录表id查询扣留奖励记录
        List<ActivityRewardRecordModel> ActivityRewardRecordModelList = activityRewardRecordDao.findSaleDeductRewardByRecordId(resp.getId());
        List<RewardResp> deductRewards = new ArrayList<>();
        for (ActivityRewardRecordModel model : ActivityRewardRecordModelList) {
            ExtendDataBean extendDataBean = JSONObject.parseObject(model.getExtendData(), ExtendDataBean.class);
            RewardResp resp1 = new RewardResp();
            resp1.setShowDate(model.getCreateTime());
            resp1.setRewardName(ActivityType.getName(model.getActivityType()));
            resp1.setRewardTypeName(RewardType.getName(model.getRewardType()));
            resp1.setRewardContent(extendDataBean.getRewardContent());
            resp1.setName(extendDataBean.getShopName());
            resp1.setRoleName(extendDataBean.getRoleName());
            resp1.setDealerCode(extendDataBean.getDealerCode());
            deductRewards.add(resp1);
        }
        return deductRewards;
    }


    //经销商处罚违约明细
    public List<RewardResp> violateDealerRewardList(FenceViolateOpenBottleAreaRecordResp resp) {
        //根据记录表id查询扣留奖励记录
        List<ActivityRewardRecordModel> ActivityRewardRecordModelList = activityRewardRecordDao.findViolateDealerRewardListByRecordId(resp.getId());
        List<RewardResp> violateDealerRewards = new ArrayList<>();
        for (ActivityRewardRecordModel model : ActivityRewardRecordModelList) {
            ExtendDataBean extendDataBean = JSONObject.parseObject(model.getExtendData(), ExtendDataBean.class);
            RewardResp resp1 = new RewardResp();
            resp1.setShowDate(model.getCreateTime());
            resp1.setRewardName(ActivityType.getName(model.getActivityType()));
            resp1.setRewardTypeName(RewardType.getName(model.getRewardType()));
            resp1.setRewardContent(extendDataBean.getRewardContent());
            resp1.setName(extendDataBean.getShopName());
            resp1.setRoleName(extendDataBean.getRoleName());
            resp1.setDealerCode(extendDataBean.getDealerCode());
            resp1.setTotal(model.getIntegral());
            resp1.setRewardNumber(extendDataBean.getMultiple());
            resp1.setConversionContent(extendDataBean.getBaseAmount());
            violateDealerRewards.add(resp1);
        }
        return violateDealerRewards;
    }

    //被侵权经销商奖励明细
    public List<RewardResp> infringedDealerRewardList(FenceViolateOpenBottleAreaRecordResp resp) {
        //根据记录表id查询扣留奖励记录
        List<ActivityRewardRecordModel> ActivityRewardRecordModelList = activityRewardRecordDao.findInfringedDealerRewardListByRecordId(resp.getId());
        List<RewardResp> infringedDealerRewards = new ArrayList<>();
        for (ActivityRewardRecordModel model : ActivityRewardRecordModelList) {
            ExtendDataBean extendDataBean = JSONObject.parseObject(model.getExtendData(), ExtendDataBean.class);
            RewardResp resp1 = new RewardResp();
            resp1.setShowDate(model.getCreateTime());
            resp1.setRewardName(ActivityType.getName(model.getActivityType()));
            resp1.setRewardTypeName(RewardType.getName(model.getRewardType()));
            resp1.setRewardContent(extendDataBean.getRewardContent());
            resp1.setName(extendDataBean.getShopName());
            resp1.setRoleName(extendDataBean.getRoleName());
            resp1.setDealerCode(extendDataBean.getDealerCode());
            resp1.setTotal(model.getIntegral());
            resp1.setRewardNumber(extendDataBean.getMultiple());
            resp1.setConversionContent(extendDataBean.getBaseAmount());
            infringedDealerRewards.add(resp1);
        }
        return infringedDealerRewards;
    }

    public List<RewardResp> appealViolateDealerRewardList(FenceViolateOpenBottleAreaRecordResp resp) {
        //根据记录表id查询扣除奖励记录
        List<ActivityRewardRecordModel> ActivityRewardRecordModelList = activityRewardRecordDao.findAppealViolateDealerRewardListByRecordId(resp.getId());
        List<RewardResp> infringedDealerRewards = new ArrayList<>();
        for (ActivityRewardRecordModel model : ActivityRewardRecordModelList) {
            ExtendDataBean extendDataBean = JSONObject.parseObject(model.getExtendData(), ExtendDataBean.class);
            RewardResp resp1 = new RewardResp();
            resp1.setShowDate(model.getCreateTime());
            resp1.setRewardName(ActivityType.getName(model.getActivityType()));
            resp1.setEventTypeName(EventType.getName(model.getEventType()));
            resp1.setRewardTypeName(RewardType.getName(model.getRewardType()));
            if (model.getEventType() == 201 || model.getEventType() == 211 || model.getEventType() == 221 || model.getEventType() == 231 || model.getEventType() == 235) {
                resp1.setRewardContent("扣除");
            } else if (model.getEventType() == 232) {
                resp1.setRewardContent("增加");
            }
            resp1.setName(extendDataBean.getShopName());
            resp1.setRoleName(extendDataBean.getRoleName());
            resp1.setDealerCode(extendDataBean.getDealerCode());
            resp1.setTotal(model.getIntegral());
            resp1.setRewardNumber(extendDataBean.getMultiple());
            resp1.setConversionContent(extendDataBean.getBaseAmount());
            infringedDealerRewards.add(resp1);
        }
        return infringedDealerRewards;
    }

    /**
     * 批量更新异常记录状态
     *
     * @param exceptionRecords 需要更新的异常记录列表
     * @param errorMsg 错误信息
     */
    private void updateExceptionRecords(List<ActivityRewardExceptionRecordModel> exceptionRecords, String errorMsg) {
        if (CollectionUtils.isEmpty(exceptionRecords)) {
            return;
        }
        
        exceptionRecords.forEach(record -> {
            record.setFenceStatus(1);
            record.setRemark(org.apache.commons.lang3.StringUtils.abbreviate("开瓶违约：" + errorMsg, 1900));
            activityRewardExceptionRecordService.updateById(record);
        });
    }
}




